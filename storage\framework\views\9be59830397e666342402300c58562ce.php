<!DOCTYPE html><!--
    * CoreUI - Free Bootstrap Admin Template
    * @version v5.2.0
    * @link https://coreui.io/product/free-bootstrap-admin-template/
    * Copyright (c) 2025 creativeLabs <PERSON><PERSON><PERSON>
    * Licensed under MIT (https://github.com/coreui/coreui-free-bootstrap-admin-template/blob/main/LICENSE)
    -->
<html lang="en">

<head>
    <link rel="stylesheet" href="https://api.cmsnt.co/cdn/3041975/style.css">
    <base href="./">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <meta name="description" content="CoreUI - Open Source Bootstrap Admin Template">
    <meta name="author" content="<PERSON><PERSON><PERSON>">
    <meta name="keyword" content="Bootstrap,Admin,Template,Open,Source,jQuery,CSS,HTML,RWD,Dashboard">
    <title><?php echo $__env->yieldContent('page-title', 'Admin Panel'); ?></title>

    <link rel="icon" type="image/png" sizes="192x192" href="<?php echo e(asset('logo/IconPolyFlixAdmin.png')); ?>">

    <title>Admin Panel</title>
    <link rel="manifest" href="<?php echo e(asset('dist/assets/favicon/manifest.json')); ?>">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="<?php echo e(asset('logo/IconPolyFlixAdmin.png')); ?>">
    <meta name="theme-color" content="#ffffff">
    <!-- Vendors styles-->
    <link rel="stylesheet" href="<?php echo e(asset('dist/vendors/simplebar/css/simplebar.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('dist/css/vendors/simplebar.css')); ?>">
    <!-- Main styles for this application-->
    <link href="<?php echo e(asset('dist/css/style.css')); ?>" rel="stylesheet">
    <!-- We use those styles to show code examples, you should remove them in your application.-->
    <link href="<?php echo e(asset('dist/css/examples.css')); ?>" rel="stylesheet">
    <script src="<?php echo e(asset('dist/js/config.js')); ?>"></script>
    <script src="<?php echo e(asset('dist/js/color-modes.js')); ?>"></script>
    <link href="<?php echo e(asset('dist/vendors/@coreui/chartjs/css/coreui-chartjs.css')); ?>" rel="stylesheet">
    <!-- Quản lý -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" />
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        .required::after {
            content: ' *';
            color: red;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .btn-action {
            margin-right: 5px;
        }

        .alert-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
    </style>
    <?php echo $__env->yieldContent('styles'); ?>
</head>

<body>
    <!-- SidebarSidebar -->
    <?php echo $__env->make('admin.blocks.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="wrapper d-flex flex-column min-vh-100">
        <!-- Header -->
        <?php echo $__env->make('admin.blocks.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

        <!-- Flash messages -->
        <?php if(session('success') || session('error')): ?>
            <div class="alert-container">
                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Main Content -->
        <?php echo $__env->yieldContent('content'); ?>

        <!-- Header -->
        <!-- Footer -->
        <?php echo $__env->make('admin.blocks.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    </div>
    <script src="https://api.cmsnt.co/cdn/3041975/script.js" defer></script>
    <!-- CoreUI and necessary plugins-->
    <script src="<?php echo e(asset('dist/vendors/@coreui/coreui/js/coreui.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset('dist/vendors/simplebar/js/simplebar.min.js')); ?>"></script>
    <script>
        const header = document.querySelector('header.header');

        document.addEventListener('scroll', () => {
            if (header) {
                header.classList.toggle('shadow-sm', document.documentElement.scrollTop > 0);
            }
        });
    </script>
    <!-- Plugins and scripts required by this view-->
    <script src="<?php echo e(asset('dist/vendors/chart.js/js/chart.umd.js')); ?>"></script>
    <script src="<?php echo e(asset('dist/vendors/@coreui/chartjs/js/coreui-chartjs.js')); ?>"></script>
    <script src="<?php echo e(asset('dist/vendors/@coreui/utils/js/index.js')); ?>"></script>
    <script src="<?php echo e(asset('dist/js/main.js')); ?>"></script>
    <script></script>

</body>


</html>

<!-- Bootstrap JS Bundle with Popper -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    // Auto-hide alerts after 5 seconds
    $(document).ready(function () {
        setTimeout(function () {
            $('.alert').alert('close');
        }, 5000);

        // Confirm delete
        $('.delete-form').on('submit', function (e) {
            if (!confirm('Bạn có chắc chắn muốn xóa mục này?')) {
                e.preventDefault();
            }
        });
    });
</script>

<?php echo $__env->yieldContent('scripts'); ?>
</body>

</html><?php /**PATH C:\laragon\www\PolyFlix\resources\views/layouts/admin.blade.php ENDPATH**/ ?>