<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    public function run(): void
    {
        User::create([
            'name' => 'Polyflix',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'vai_tro_id' => 1,
            'hoat_dong' => 1
        ]);

        User::create([
            'name' => 'hieu2',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'vai_tro_id' => 2,
            'hoat_dong' => 1
        ]);

        User::create([
            'name' => 'hieu3',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'vai_tro_id' => 1,
            'hoat_dong' => 1
        ]);
    }
}